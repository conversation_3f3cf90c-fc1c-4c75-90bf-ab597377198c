{"name": "piggy-portfolio", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npx tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts"}, "repository": {"type": "git", "url": "git+https://github.com/Qasim-168/Piggy-Portfolio.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Qasim-168/Piggy-Portfolio/issues"}, "homepage": "https://github.com/Qasim-168/Piggy-Portfolio#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "mongoose": "^8.16.5", "ws": "^8.18.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/mongoose": "^5.11.96", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}